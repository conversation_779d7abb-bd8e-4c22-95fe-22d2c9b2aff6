#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流线可视化器
负责创建流线和速度矢量的可视化
"""

import vtk
import numpy as np
import pandas as pd
from typing import Optional, Tuple, List, Dict, Any
from vtk_renderer import VT<PERSON><PERSON>enderer
from scipy.spatial import cKDTree
from concurrent.futures import ThreadPoolExecutor
from vtk.util import numpy_support


class StreamlineVisualizer(VTKRenderer):
    """流线可视化器"""

    def __init__(self):
        super().__init__()
        self.kdtree = None
        self.velocity_data = None
        self.velocity_magnitudes = None
        
    def create_streamlines(self, ugrid: vtk.vtkUnstructuredGrid,
                          density: int = 50, region_name: str = "",
                          start_from_inlets: bool = True, max_points: int = 25) -> Optional[vtk.vtkActor]:
        """
        创建流线可视化
        
        Args:
            ugrid: VTK非结构化网格
            density: 流线密度
            
        Returns:
            vtk.vtkActor: 流线actor或None
        """
        try:
            # 检查是否有矢量数据
            if ugrid.GetPointData().GetVectors() is None:
                print("警告: 没有找到矢量数据，无法创建流线")
                return None

            # 检查网格类型并尝试转换为适合流线追踪的格式
            processed_grid = self._prepare_grid_for_streamlines(ugrid)
            if processed_grid is None:
                print("警告: 无法准备适合流线追踪的网格")
                return None
                
            # 检查速度场的范围
            vectors = processed_grid.GetPointData().GetVectors()
            vector_range = vectors.GetRange(-1)  # 获取矢量幅度范围
            print(f"速度场范围: {vector_range[0]:.6f} ~ {vector_range[1]:.6f}")

            if vector_range[1] < 1e-10:  # 速度场太小
                print("警告: 速度场幅度太小，无法生成有效流线")
                return None

            bounds = processed_grid.GetBounds()

            # 详细检查处理后的网格
            print(f"处理后网格: {processed_grid.GetNumberOfPoints()} 个点, {processed_grid.GetNumberOfCells()} 个单元")
            print(f"网格边界: [{bounds[0]:.3f}, {bounds[1]:.3f}] x [{bounds[2]:.3f}, {bounds[3]:.3f}] x [{bounds[4]:.3f}, {bounds[5]:.3f}]")
            print(f"网格类型: {processed_grid.GetClassName()}")

            # 检查网格连接性
            if processed_grid.GetNumberOfCells() > 0:
                cell_types = set()
                for i in range(min(5, processed_grid.GetNumberOfCells())):
                    cell_type = processed_grid.GetCellType(i)
                    cell_types.add(cell_type)
                print(f"单元类型: {cell_types}")

            # 检查矢量数据的有效性
            num_valid_vectors = 0
            num_zero_vectors = 0
            for i in range(min(100, processed_grid.GetNumberOfPoints())):  # 检查前100个点
                vec = vectors.GetTuple3(i)
                magnitude = (vec[0]**2 + vec[1]**2 + vec[2]**2)**0.5
                if not (magnitude != magnitude or magnitude == float('inf')):  # 不是NaN或inf
                    num_valid_vectors += 1
                    if magnitude < 1e-10:
                        num_zero_vectors += 1

            print(f"矢量数据检查: {num_valid_vectors}/100 有效, {num_zero_vectors} 接近零")

            # 简化种子点策略 - 只使用高速度点策略
            print(f"区域 {region_name}: 使用简化种子策略")

            # 只使用一种最有效的种子策略
            seed_source = self._create_seeds_from_high_velocity_points(processed_grid, density)
            if seed_source is None:
                print("❌ 无法创建种子点")
                return None

            # 创建单一的高效流线追踪器 - 只使用RK4
            tracer = vtk.vtkStreamTracer()
            tracer.SetInputData(processed_grid)
            tracer.SetSourceConnection(seed_source.GetOutputPort())
            tracer.SetIntegratorTypeToRungeKutta4()  # 只使用RK4积分器

            # 设置流线追踪参数
            domain_size = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])

            # 获取速度范围来调整参数
            vectors = processed_grid.GetPointData().GetVectors()
            if vectors:
                velocity_range = vectors.GetRange(-1)
                avg_velocity = max(velocity_range[1], 1e-6)
                time_step = domain_size / (avg_velocity * 500)  # 优化的时间步长
            else:
                time_step = domain_size / 500

            # 设置追踪参数
            max_propagation = domain_size * 30  # 大传播距离
            max_steps = 15000  # 大步数

            tracer.SetMaximumPropagation(max_propagation)
            tracer.SetInitialIntegrationStep(time_step)
            tracer.SetMaximumIntegrationStep(time_step * 2)
            tracer.SetMinimumIntegrationStep(time_step / 10)
            tracer.SetIntegrationDirectionToBoth()
            tracer.SetMaximumNumberOfSteps(max_steps)
            tracer.SetTerminalSpeed(1e-12)

            print(f"流线追踪参数: 域大小={domain_size:.3f}, 时间步长={time_step:.6f}, 最大传播={max_propagation:.3f}, 最大步数={max_steps}")

            # 执行流线追踪
            try:
                tracer.Update()
                output = tracer.GetOutput()
                num_points = output.GetNumberOfPoints()
                num_lines = output.GetNumberOfLines()

                print(f"流线追踪结果: {num_points} 个点, {num_lines} 条流线")

                if num_points == 0:
                    print("❌ 没有生成流线")
                    self._diagnose_streamline_failure(tracer, processed_grid, seed_source)
                    return None

                best_streamline = tracer

            except Exception as e:
                print(f"流线追踪失败: {e}")
                import traceback
                traceback.print_exc()
                return None
            
            # 创建流线管道
            tube = vtk.vtkTubeFilter()
            tube.SetInputConnection(best_streamline.GetOutputPort())

            # 自适应管道半径 - 使用更大的半径
            domain_size = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
            tube_radius = domain_size / 200  # 增加管道半径
            tube.SetRadius(tube_radius)
            tube.SetNumberOfSides(8)  # 增加管道侧面数

            print(f"流线管道: 半径={tube_radius:.6f}, 域大小={domain_size:.3f}")
            
            # 检查是否有流线数据
            streamline_output = best_streamline.GetOutput()
            num_lines = streamline_output.GetNumberOfLines()
            num_points = streamline_output.GetNumberOfPoints()

            print(f"流线详细信息: {num_lines} 条流线, {num_points} 个点")

            # 详细分析每条流线
            if num_lines > 0:
                self._analyze_streamlines(streamline_output)

            if num_lines == 0:
                print("警告: 没有生成流线，尝试直接渲染点")
                # 如果没有流线，直接渲染点
                mapper = vtk.vtkPolyDataMapper()
                mapper.SetInputConnection(best_streamline.GetOutputPort())

                actor = vtk.vtkActor()
                actor.SetMapper(mapper)
                actor.GetProperty().SetColor(1, 0, 0)  # 红色
                actor.GetProperty().SetPointSize(5)
                actor.GetProperty().SetRepresentationToPoints()

                return actor
            else:
                # 有流线，检查是否需要特殊处理
                print(f"成功生成 {num_lines} 条流线")

                # 尝试后处理流线以改善质量
                processed_streamlines = self._post_process_streamlines(streamline_output, domain_size)

                # 使用处理后的流线进行渲染
                return self._render_streamlines_adaptive(processed_streamlines, tube, domain_size)
            
        except Exception as e:
            print(f"创建流线可视化失败: {e}")
            return None
            
    def create_velocity_vectors_legacy(self, ugrid: vtk.vtkUnstructuredGrid,
                                       scale_factor: float = 1.0) -> Optional[vtk.vtkActor]:
        """
        创建速度矢量可视化（旧版本，保持兼容性）

        Args:
            ugrid: VTK非结构化网格
            scale_factor: 矢量缩放因子

        Returns:
            vtk.vtkActor: 矢量actor或None
        """
        try:
            # 检查是否有矢量数据
            if ugrid.GetPointData().GetVectors() is None:
                print("警告: 没有找到矢量数据，无法创建矢量可视化")
                return None

            # 创建子采样以减少矢量数量
            subsample = vtk.vtkMaskPoints()
            subsample.SetInputData(ugrid)
            subsample.SetOnRatio(max(1, ugrid.GetNumberOfPoints() // 1000))  # 最多显示1000个矢量
            subsample.RandomModeOn()

            # 创建箭头字形
            arrow = vtk.vtkArrowSource()
            arrow.SetTipResolution(6)
            arrow.SetShaftResolution(6)

            # 创建字形过滤器
            glyph = vtk.vtkGlyph3D()
            glyph.SetInputConnection(subsample.GetOutputPort())
            glyph.SetSourceConnection(arrow.GetOutputPort())
            glyph.SetVectorModeToUseVector()
            glyph.SetScaleModeToScaleByVector()
            glyph.SetScaleFactor(scale_factor / 100.0)  # 调整缩放因子

            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(glyph.GetOutputPort())

            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetColor(0, 0, 1)  # 蓝色矢量

            return actor
            
        except Exception as e:
            print(f"创建矢量可视化失败: {e}")
            return None
            
    def _create_seeds_from_high_velocity_points(self, ugrid: vtk.vtkUnstructuredGrid, 
                                              density: int) -> Optional[vtk.vtkProgrammableSource]:
        """从高速度点创建种子点"""
        try:
            vectors = ugrid.GetPointData().GetVectors()
            if vectors is None:
                print("高速度种子策略: 没有矢量数据")
                return None

            # 计算速度幅度
            num_points = ugrid.GetNumberOfPoints()
            high_velocity_points = vtk.vtkPoints()

            vector_range = vectors.GetRange(-1)

            # 使用多级阈值策略
            thresholds = [
                vector_range[1] * 0.3,  # 30%最大速度
                vector_range[1] * 0.1,  # 10%最大速度
                vector_range[1] * 0.05, # 5%最大速度
                vector_range[1] * 0.01  # 1%最大速度
            ]

            count = 0
            print(f"高速度种子策略: 总点数={num_points}, 速度范围={vector_range[0]:.6f}~{vector_range[1]:.6f}")

            # 尝试不同的阈值，直到找到足够的种子点
            for threshold in thresholds:
                print(f"尝试阈值: {threshold:.6f}")
                temp_count = 0

                for i in range(num_points):
                    velocity = vectors.GetTuple(i)
                    magnitude = (velocity[0]**2 + velocity[1]**2 + velocity[2]**2)**0.5

                    if magnitude > threshold:
                        # 如果是第一次尝试这个阈值，或者之前的点数不够，添加点
                        if count < density:
                            point = ugrid.GetPoint(i)
                            high_velocity_points.InsertNextPoint(point)
                            count += 1
                            temp_count += 1

                        if count >= density:
                            break

                print(f"阈值 {threshold:.6f}: 新增 {temp_count} 个点，总计 {count} 个点")

                # 如果找到足够的点，停止尝试更低的阈值
                if count >= max(1, density // 4):  # 至少找到25%的目标数量
                    break

            print(f"高速度种子策略: 最终找到 {high_velocity_points.GetNumberOfPoints()} 个高速度点")

            if high_velocity_points.GetNumberOfPoints() == 0:
                print("高速度种子策略: 没有找到符合条件的高速度点")
                return None
                
            # 创建种子源
            seed_source = vtk.vtkPolyData()
            seed_source.SetPoints(high_velocity_points)
            
            # 创建顶点
            vertices = vtk.vtkCellArray()
            for i in range(high_velocity_points.GetNumberOfPoints()):
                vertices.InsertNextCell(1, [i])
            seed_source.SetVerts(vertices)
            
            # 转换为算法输出
            seed_filter = vtk.vtkProgrammableSource()
            seed_filter.SetExecuteMethod(lambda: seed_filter.GetPolyDataOutput().ShallowCopy(seed_source))
            
            return seed_filter
            
        except Exception as e:
            print(f"创建高速度种子点失败: {e}")
            return None
            
    def _create_seeds_near_boundaries(self, bounds: Tuple[float, float, float, float, float, float],
                                    density: int) -> Optional[vtk.vtkPointSource]:
        """在边界附近创建种子点"""
        try:
            seed_points = vtk.vtkPointSource()

            # 在入口边界附近创建种子点
            center_x = bounds[0] + (bounds[1] - bounds[0]) * 0.1  # 靠近X最小边界
            center_y = (bounds[2] + bounds[3]) / 2
            center_z = (bounds[4] + bounds[5]) / 2
            radius = (bounds[3] - bounds[2]) / 4
            num_seeds = max(1, density // 2)

            seed_points.SetCenter(center_x, center_y, center_z)
            seed_points.SetRadius(radius)
            seed_points.SetNumberOfPoints(num_seeds)

            print(f"边界种子策略: 中心=({center_x:.3f}, {center_y:.3f}, {center_z:.3f}), 半径={radius:.3f}, 种子数={num_seeds}")

            return seed_points

        except Exception as e:
            print(f"创建边界种子点失败: {e}")
            return None
            
    def _create_seeds_in_center(self, bounds: Tuple[float, float, float, float, float, float],
                              density: int) -> Optional[vtk.vtkPointSource]:
        """在中心区域创建种子点"""
        try:
            seed_points = vtk.vtkPointSource()

            center_x = (bounds[0] + bounds[1]) / 2
            center_y = (bounds[2] + bounds[3]) / 2
            center_z = (bounds[4] + bounds[5]) / 2

            radius = min(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4]) / 6
            num_seeds = max(1, density // 3)

            seed_points.SetCenter(center_x, center_y, center_z)
            seed_points.SetRadius(radius)
            seed_points.SetNumberOfPoints(num_seeds)

            print(f"中心种子策略: 中心=({center_x:.3f}, {center_y:.3f}, {center_z:.3f}), 半径={radius:.3f}, 种子数={num_seeds}")

            return seed_points

        except Exception as e:
            print(f"创建中心种子点失败: {e}")
            return None

    def _create_seeds_from_region_interior(self, ugrid: vtk.vtkUnstructuredGrid,
                                         bounds: Tuple[float, float, float, float, float, float],
                                         density: int) -> Optional[vtk.vtkPointSource]:
        """
        在区域内部创建种子点（专门用于入口区域）
        """
        try:
            seed_points = vtk.vtkPointSource()

            # 在区域内部创建种子点，避开边界
            x_range = bounds[1] - bounds[0]
            y_range = bounds[3] - bounds[2]
            z_range = bounds[5] - bounds[4]

            center_x = bounds[0] + x_range * 0.5
            center_y = bounds[2] + y_range * 0.5
            center_z = bounds[4] + z_range * 0.5

            # 使用较小的半径，确保种子点在区域内部
            radius = min(x_range, y_range, z_range) * 0.3
            num_seeds = max(1, density // 2)

            seed_points.SetCenter(center_x, center_y, center_z)
            seed_points.SetRadius(radius)
            seed_points.SetNumberOfPoints(num_seeds)

            print(f"区域内部种子策略: 中心=({center_x:.3f}, {center_y:.3f}, {center_z:.3f}), 半径={radius:.3f}, 种子数={num_seeds}")

            return seed_points

        except Exception as e:
            print(f"创建区域内部种子点失败: {e}")
            return None

    def create_velocity_vectors(self, ugrid: vtk.vtkUnstructuredGrid,
                               scale: float = 1.0, use_velocity_coloring: bool = True) -> Optional[vtk.vtkActor]:
        """
        创建速度矢量可视化

        Args:
            ugrid: VTK非结构化网格
            scale: 矢量缩放因子
            use_velocity_coloring: 是否使用速度大小进行颜色映射

        Returns:
            vtk.vtkActor: 矢量actor或None
        """
        try:
            # 检查是否有矢量数据
            if ugrid.GetPointData().GetVectors() is None:
                print("警告: 没有找到矢量数据，无法创建矢量可视化")
                return None

            # 检查速度场的范围
            vectors = ugrid.GetPointData().GetVectors()
            vector_range = vectors.GetRange(-1)  # 获取矢量幅度范围
            print(f"矢量场范围: {vector_range[0]:.6f} ~ {vector_range[1]:.6f}")

            if vector_range[1] < 1e-10:  # 速度场太小
                print("警告: 矢量场幅度太小，无法生成有效矢量")
                return None

            # 如果需要颜色映射，先添加速度大小标量数据
            if use_velocity_coloring:
                # 计算速度大小并添加为标量数据
                vectors = ugrid.GetPointData().GetVectors()
                velocity_magnitudes = vtk.vtkFloatArray()
                velocity_magnitudes.SetNumberOfComponents(1)
                velocity_magnitudes.SetName("VelocityMagnitude")

                for i in range(ugrid.GetNumberOfPoints()):
                    vector = vectors.GetTuple3(i)
                    magnitude = np.sqrt(vector[0]**2 + vector[1]**2 + vector[2]**2)
                    velocity_magnitudes.InsertNextValue(magnitude)

                ugrid.GetPointData().SetScalars(velocity_magnitudes)
                ugrid.GetPointData().SetActiveScalars("VelocityMagnitude")

            # 创建子采样以减少矢量数量
            subsample = vtk.vtkMaskPoints()
            subsample.SetInputData(ugrid)
            subsample.SetOnRatio(max(1, ugrid.GetNumberOfPoints() // 500))  # 最多显示500个矢量
            subsample.RandomModeOn()

            # 创建箭头字形
            arrow = vtk.vtkArrowSource()
            arrow.SetTipResolution(6)
            arrow.SetShaftResolution(6)

            # 创建字形过滤器
            glyph = vtk.vtkGlyph3D()
            glyph.SetInputConnection(subsample.GetOutputPort())
            glyph.SetSourceConnection(arrow.GetOutputPort())
            glyph.SetVectorModeToUseVector()
            glyph.SetScaleModeToScaleByVector()
            if use_velocity_coloring:
                glyph.SetColorModeToColorByScalar()

            # 自适应缩放因子
            bounds = ugrid.GetBounds()
            domain_size = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])
            auto_scale = domain_size / (vector_range[1] * 10)  # 自动计算合适的缩放
            glyph.SetScaleFactor(scale * auto_scale)

            # 创建映射器
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(glyph.GetOutputPort())

            # 如果启用速度颜色映射
            if use_velocity_coloring:
                # 启用标量可视化
                mapper.SetScalarModeToUsePointData()
                mapper.ScalarVisibilityOn()
                mapper.SelectColorArray("VelocityMagnitude")

                # 如果有预设的速度颜色查找表，使用它
                if hasattr(self, 'velocity_lookup_table') and self.velocity_lookup_table:
                    mapper.SetLookupTable(self.velocity_lookup_table)
                    if hasattr(self, 'velocity_range') and self.velocity_range:
                        mapper.SetScalarRange(self.velocity_range[0], self.velocity_range[1])
                        print(f"矢量使用预设速度颜色映射: {self.velocity_range[0]:.3f} - {self.velocity_range[1]:.3f} m/s")
                    else:
                        mapper.SetScalarRange(vector_range[0], vector_range[1])
                        print(f"矢量使用预设查找表，默认范围: {vector_range[0]:.6f} - {vector_range[1]:.6f}")
                else:
                    # 创建与流线相同的彩虹色谱查找表
                    lut = vtk.vtkLookupTable()
                    lut.SetNumberOfTableValues(256)
                    lut.SetHueRange(0.667, 0.0)  # 蓝色到红色
                    lut.SetSaturationRange(1.0, 1.0)
                    lut.SetValueRange(1.0, 1.0)
                    lut.SetAlphaRange(1.0, 1.0)
                    lut.SetRange(vector_range[0], vector_range[1])
                    lut.Build()

                    mapper.SetLookupTable(lut)
                    mapper.SetScalarRange(vector_range[0], vector_range[1])
                    print(f"矢量使用新建速度颜色映射: {vector_range[0]:.6f} - {vector_range[1]:.6f}")
            else:
                # 使用默认蓝色
                mapper.ScalarVisibilityOff()

            # 创建演员
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)

            # 只有在不使用颜色映射时才设置固定颜色
            if not use_velocity_coloring:
                actor.GetProperty().SetColor(0, 0, 1)  # 蓝色矢量（当没有颜色映射时）

            print(f"✓ 速度矢量创建完成，缩放因子: {scale * auto_scale:.6f}")
            return actor

        except Exception as e:
            print(f"创建速度矢量可视化失败: {e}")
            return None

    def _create_seeds_from_moderate_velocity_points(self, ugrid: vtk.vtkUnstructuredGrid,
                                                  density: int) -> Optional[vtk.vtkProgrammableSource]:
        """从中等速度点创建种子点（降低阈值）"""
        try:
            vectors = ugrid.GetPointData().GetVectors()
            if vectors is None:
                print("中等速度种子策略: 没有矢量数据")
                return None

            # 计算速度幅度
            num_points = ugrid.GetNumberOfPoints()
            moderate_velocity_points = vtk.vtkPoints()

            vector_range = vectors.GetRange(-1)
            # 使用更低的阈值：10%最大速度
            threshold = vector_range[1] * 0.1
            count = 0

            print(f"中等速度种子策略: 总点数={num_points}, 速度范围={vector_range[0]:.6f}~{vector_range[1]:.6f}, 阈值={threshold:.6f}")

            for i in range(num_points):
                velocity = vectors.GetTuple(i)
                magnitude = (velocity[0]**2 + velocity[1]**2 + velocity[2]**2)**0.5

                if magnitude > threshold:
                    point = ugrid.GetPoint(i)
                    moderate_velocity_points.InsertNextPoint(point)
                    count += 1

                    if count >= density:
                        break

            print(f"中等速度种子策略: 找到 {moderate_velocity_points.GetNumberOfPoints()} 个中等速度点")

            if moderate_velocity_points.GetNumberOfPoints() == 0:
                print("中等速度种子策略: 没有找到符合条件的中等速度点")
                return None

            # 创建种子源
            seed_source = vtk.vtkPolyData()
            seed_source.SetPoints(moderate_velocity_points)

            # 创建顶点
            vertices = vtk.vtkCellArray()
            for i in range(moderate_velocity_points.GetNumberOfPoints()):
                vertices.InsertNextCell(1, [i])
            seed_source.SetVerts(vertices)

            # 转换为算法输出
            seed_filter = vtk.vtkProgrammableSource()
            seed_filter.SetExecuteMethod(lambda: seed_filter.GetPolyDataOutput().ShallowCopy(seed_source))

            return seed_filter

        except Exception as e:
            print(f"创建中等速度种子点失败: {e}")
            return None

    def _create_seeds_distributed_in_region(self, ugrid: vtk.vtkUnstructuredGrid,
                                           bounds: Tuple[float, float, float, float, float, float],
                                           density: int) -> Optional[vtk.vtkProgrammableSource]:
        """在区域内分布式创建种子点"""
        try:
            # 从网格中均匀采样点作为种子
            num_points = ugrid.GetNumberOfPoints()
            if num_points == 0:
                print("分布式种子策略: 网格没有点")
                return None

            # 计算采样间隔
            sample_interval = max(1, num_points // density)
            distributed_points = vtk.vtkPoints()

            print(f"分布式种子策略: 总点数={num_points}, 采样间隔={sample_interval}")

            count = 0
            for i in range(0, num_points, sample_interval):
                if count >= density:
                    break

                point = ugrid.GetPoint(i)
                distributed_points.InsertNextPoint(point)
                count += 1

            print(f"分布式种子策略: 创建了 {distributed_points.GetNumberOfPoints()} 个分布式种子点")

            if distributed_points.GetNumberOfPoints() == 0:
                print("分布式种子策略: 没有创建任何种子点")
                return None

            # 创建种子源
            seed_source = vtk.vtkPolyData()
            seed_source.SetPoints(distributed_points)

            # 创建顶点
            vertices = vtk.vtkCellArray()
            for i in range(distributed_points.GetNumberOfPoints()):
                vertices.InsertNextCell(1, [i])
            seed_source.SetVerts(vertices)

            # 转换为算法输出
            seed_filter = vtk.vtkProgrammableSource()
            seed_filter.SetExecuteMethod(lambda: seed_filter.GetPolyDataOutput().ShallowCopy(seed_source))

            return seed_filter

        except Exception as e:
            print(f"创建分布式种子点失败: {e}")
            return None

    def _create_seeds_near_boundaries_adaptive(self, bounds: Tuple[float, float, float, float, float, float],
                                             density: int) -> Optional[vtk.vtkPointSource]:
        """自适应边界种子点创建（不假设特定的入口方向）"""
        try:
            seed_points = vtk.vtkPointSource()

            # 计算区域尺寸
            x_range = bounds[1] - bounds[0]
            y_range = bounds[3] - bounds[2]
            z_range = bounds[5] - bounds[4]

            # 找到最大的维度，在该维度的边界附近创建种子
            max_dim = max(x_range, y_range, z_range)

            if max_dim == x_range:
                # X方向最大，在X边界附近创建种子
                center_x = bounds[0] + x_range * 0.2  # 靠近X最小边界
                center_y = (bounds[2] + bounds[3]) / 2
                center_z = (bounds[4] + bounds[5]) / 2
                radius = min(y_range, z_range) / 4
            elif max_dim == y_range:
                # Y方向最大，在Y边界附近创建种子
                center_x = (bounds[0] + bounds[1]) / 2
                center_y = bounds[2] + y_range * 0.2  # 靠近Y最小边界
                center_z = (bounds[4] + bounds[5]) / 2
                radius = min(x_range, z_range) / 4
            else:
                # Z方向最大，在Z边界附近创建种子
                center_x = (bounds[0] + bounds[1]) / 2
                center_y = (bounds[2] + bounds[3]) / 2
                center_z = bounds[4] + z_range * 0.2  # 靠近Z最小边界
                radius = min(x_range, y_range) / 4

            num_seeds = max(1, density // 2)

            seed_points.SetCenter(center_x, center_y, center_z)
            seed_points.SetRadius(radius)
            seed_points.SetNumberOfPoints(num_seeds)

            print(f"自适应边界种子策略: 中心=({center_x:.3f}, {center_y:.3f}, {center_z:.3f}), 半径={radius:.3f}, 种子数={num_seeds}")
            print(f"区域尺寸: X={x_range:.3f}, Y={y_range:.3f}, Z={z_range:.3f}, 最大维度={'X' if max_dim == x_range else 'Y' if max_dim == y_range else 'Z'}")

            return seed_points

        except Exception as e:
            print(f"创建自适应边界种子点失败: {e}")
            return None

    def _evaluate_streamline_quality(self, streamline_output: vtk.vtkPolyData, domain_size: float) -> float:
        """
        评估流线质量

        Args:
            streamline_output: 流线输出数据
            domain_size: 域大小

        Returns:
            float: 质量评分（越高越好）
        """
        try:
            num_points = streamline_output.GetNumberOfPoints()
            num_lines = streamline_output.GetNumberOfLines()

            if num_points == 0 or num_lines == 0:
                return 0.0

            # 计算流线总长度
            total_length = 0.0
            max_line_length = 0.0

            lines = streamline_output.GetLines()
            lines.InitTraversal()

            while True:
                cell = vtk.vtkIdList()
                if not lines.GetNextCell(cell):
                    break

                line_length = 0.0
                for i in range(cell.GetNumberOfIds() - 1):
                    p1 = streamline_output.GetPoint(cell.GetId(i))
                    p2 = streamline_output.GetPoint(cell.GetId(i + 1))

                    # 计算两点间距离
                    dx = p2[0] - p1[0]
                    dy = p2[1] - p1[1]
                    dz = p2[2] - p1[2]
                    segment_length = (dx*dx + dy*dy + dz*dz)**0.5
                    line_length += segment_length

                total_length += line_length
                max_line_length = max(max_line_length, line_length)

            # 质量评分考虑因素：
            # 1. 流线数量（更多流线更好，但有上限）
            line_score = min(num_lines / 10.0, 1.0)  # 最多10条流线得满分

            # 2. 平均流线长度相对于域大小的比例（更长的流线更好）
            avg_length = total_length / num_lines if num_lines > 0 else 0
            length_score = min(avg_length / domain_size, 2.0)  # 长度达到2倍域大小得满分

            # 3. 最长流线长度（确保至少有一条长流线）
            max_length_score = min(max_line_length / domain_size, 3.0)  # 最长流线达到3倍域大小得满分

            # 4. 点密度（适中的点密度更好）
            avg_points_per_line = num_points / num_lines if num_lines > 0 else 0
            density_score = min(avg_points_per_line / 50.0, 1.0)  # 每条流线50个点得满分

            # 综合评分
            quality_score = (line_score * 0.2 +
                           length_score * 0.3 +
                           max_length_score * 0.4 +
                           density_score * 0.1)

            return quality_score

        except Exception as e:
            print(f"评估流线质量失败: {e}")
            return 0.0

    def _analyze_streamlines(self, streamline_output: vtk.vtkPolyData):
        """分析流线详细信息"""
        try:
            lines = streamline_output.GetLines()
            lines.InitTraversal()

            line_lengths = []
            line_point_counts = []

            line_idx = 0
            while True:
                cell = vtk.vtkIdList()
                if not lines.GetNextCell(cell):
                    break

                num_points_in_line = cell.GetNumberOfIds()
                line_point_counts.append(num_points_in_line)

                # 计算流线长度
                line_length = 0.0
                for i in range(num_points_in_line - 1):
                    p1 = streamline_output.GetPoint(cell.GetId(i))
                    p2 = streamline_output.GetPoint(cell.GetId(i + 1))

                    dx = p2[0] - p1[0]
                    dy = p2[1] - p1[1]
                    dz = p2[2] - p1[2]
                    segment_length = (dx*dx + dy*dy + dz*dz)**0.5
                    line_length += segment_length

                line_lengths.append(line_length)

                if line_idx < 5:  # 只打印前5条流线的详细信息
                    print(f"流线 {line_idx}: {num_points_in_line} 个点, 长度={line_length:.6f}")

                line_idx += 1

            if line_lengths:
                avg_length = sum(line_lengths) / len(line_lengths)
                max_length = max(line_lengths)
                min_length = min(line_lengths)
                avg_points = sum(line_point_counts) / len(line_point_counts)

                print(f"流线统计: 平均长度={avg_length:.6f}, 最大长度={max_length:.6f}, 最小长度={min_length:.6f}")
                print(f"点数统计: 平均每条流线 {avg_points:.1f} 个点")

                # 检查是否有很多短流线
                short_lines = sum(1 for length in line_lengths if length < avg_length * 0.1)
                if short_lines > len(line_lengths) * 0.5:
                    print(f"警告: 检测到 {short_lines} 条短流线（占总数的 {short_lines/len(line_lengths)*100:.1f}%）")
                    print("这可能导致流线显示为短圆柱形")

        except Exception as e:
            print(f"分析流线失败: {e}")

    def _render_streamlines_adaptive(self, streamline_output: vtk.vtkPolyData,
                                   tube: vtk.vtkTubeFilter, domain_size: float) -> vtk.vtkActor:
        """自适应流线渲染"""
        try:
            lines = streamline_output.GetLines()
            num_lines = streamline_output.GetNumberOfLines()

            # 分析流线长度分布
            line_lengths = []
            lines.InitTraversal()

            while True:
                cell = vtk.vtkIdList()
                if not lines.GetNextCell(cell):
                    break

                line_length = 0.0
                for i in range(cell.GetNumberOfIds() - 1):
                    p1 = streamline_output.GetPoint(cell.GetId(i))
                    p2 = streamline_output.GetPoint(cell.GetId(i + 1))

                    dx = p2[0] - p1[0]
                    dy = p2[1] - p1[1]
                    dz = p2[2] - p1[2]
                    line_length += (dx*dx + dy*dy + dz*dz)**0.5

                line_lengths.append(line_length)

            if line_lengths:
                avg_length = sum(line_lengths) / len(line_lengths)
                short_line_ratio = sum(1 for length in line_lengths if length < domain_size * 0.01) / len(line_lengths)

                print(f"流线渲染分析: 平均长度={avg_length:.6f}, 短流线比例={short_line_ratio:.2f}")

                # 如果短流线太多，使用线条渲染而不是管道渲染
                if short_line_ratio > 0.7 or avg_length < domain_size * 0.005:
                    print("检测到大量短流线，使用线条渲染")
                    return self._render_as_lines(streamline_output)
                else:
                    print("使用管道渲染")
                    return self._render_as_tubes(tube)
            else:
                return self._render_as_tubes(tube)

        except Exception as e:
            print(f"自适应渲染失败: {e}")
            return self._render_as_tubes(tube)

    def _render_as_lines(self, streamline_output: vtk.vtkPolyData) -> vtk.vtkActor:
        """将流线渲染为线条"""
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputData(streamline_output)

        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        actor.GetProperty().SetColor(1, 0, 0)  # 红色
        actor.GetProperty().SetLineWidth(3)  # 增加线宽
        actor.GetProperty().SetRepresentationToWireframe()

        print("流线渲染为线条，线宽=3")
        return actor

    def _render_as_tubes(self, tube: vtk.vtkTubeFilter) -> vtk.vtkActor:
        """将流线渲染为管道"""
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(tube.GetOutputPort())

        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        actor.GetProperty().SetColor(1, 0, 0)  # 红色

        print("流线渲染为管道")
        return actor

    def _post_process_streamlines(self, streamline_output: vtk.vtkPolyData, domain_size: float) -> vtk.vtkPolyData:
        """后处理流线以改善质量"""
        try:
            # 首先尝试清理流线数据
            cleaner = vtk.vtkCleanPolyData()
            cleaner.SetInputData(streamline_output)
            cleaner.Update()

            cleaned_output = cleaner.GetOutput()

            # 检查清理后的结果
            original_lines = streamline_output.GetNumberOfLines()
            cleaned_lines = cleaned_output.GetNumberOfLines()

            print(f"流线清理: {original_lines} -> {cleaned_lines} 条流线")

            # 如果清理后流线数量显著减少，可能是有重复或无效的流线
            if cleaned_lines < original_lines * 0.8:
                print("流线清理移除了大量重复或无效流线")

            # 尝试使用连接过滤器连接相近的流线端点
            if cleaned_lines > 1:
                connectivity = vtk.vtkConnectivityFilter()
                connectivity.SetInputData(cleaned_output)
                connectivity.SetExtractionModeToAllRegions()
                connectivity.Update()

                connected_output = connectivity.GetOutput()
                connected_lines = connected_output.GetNumberOfLines()

                print(f"流线连接: {cleaned_lines} -> {connected_lines} 条流线")

                # 如果连接后流线数量减少，说明成功连接了一些流线
                if connected_lines < cleaned_lines:
                    print("成功连接了一些流线段")
                    return connected_output

            return cleaned_output

        except Exception as e:
            print(f"流线后处理失败: {e}")
            return streamline_output

    def _diagnose_streamline_failure(self, tracer: vtk.vtkStreamTracer,
                                   ugrid: vtk.vtkUnstructuredGrid,
                                   seed_source) -> None:
        """诊断流线生成失败的原因"""
        try:
            print("=== 流线失败诊断 ===")

            # 检查输入网格
            print(f"输入网格: {ugrid.GetNumberOfPoints()} 个点, {ugrid.GetNumberOfCells()} 个单元")

            # 检查矢量数据
            vectors = ugrid.GetPointData().GetVectors()
            if vectors is None:
                print("❌ 没有矢量数据")
                return
            else:
                print(f"✓ 矢量数据: {vectors.GetNumberOfTuples()} 个元组, {vectors.GetNumberOfComponents()} 个组件")

            # 检查种子点
            if seed_source is None:
                print("❌ 没有种子源")
                return

            try:
                seed_source.Update()
                seed_output = seed_source.GetOutput()
                if hasattr(seed_output, 'GetNumberOfPoints'):
                    seed_points = seed_output.GetNumberOfPoints()
                    print(f"✓ 种子点: {seed_points} 个")

                    if seed_points > 0:
                        # 检查种子点是否在网格边界内
                        bounds = ugrid.GetBounds()
                        print(f"网格边界: [{bounds[0]:.3f}, {bounds[1]:.3f}] x [{bounds[2]:.3f}, {bounds[3]:.3f}] x [{bounds[4]:.3f}, {bounds[5]:.3f}]")

                        # 检查前几个种子点的位置
                        for i in range(min(3, seed_points)):
                            point = seed_output.GetPoint(i)
                            in_bounds = (bounds[0] <= point[0] <= bounds[1] and
                                       bounds[2] <= point[1] <= bounds[3] and
                                       bounds[4] <= point[2] <= bounds[5])
                            print(f"种子点 {i}: ({point[0]:.3f}, {point[1]:.3f}, {point[2]:.3f}) {'✓' if in_bounds else '❌'}")
                else:
                    print("❌ 种子源输出不是点数据")
            except Exception as e:
                print(f"❌ 种子源更新失败: {e}")

            # 检查追踪器参数
            print(f"追踪器参数:")
            print(f"  最大传播: {tracer.GetMaximumPropagation():.3f}")
            print(f"  最大步数: {tracer.GetMaximumNumberOfSteps()}")
            print(f"  初始步长: {tracer.GetInitialIntegrationStep():.6f}")
            print(f"  最大步长: {tracer.GetMaximumIntegrationStep():.6f}")
            print(f"  最小步长: {tracer.GetMinimumIntegrationStep():.6f}")
            print(f"  终止速度: {tracer.GetTerminalSpeed():.12f}")

            # 检查网格类型是否适合流线追踪
            cell_types = set()
            for i in range(min(10, ugrid.GetNumberOfCells())):
                cell_type = ugrid.GetCellType(i)
                cell_types.add(cell_type)

            print(f"网格单元类型: {cell_types}")

            # VTK单元类型说明
            type_names = {
                1: "顶点", 3: "线段", 5: "三角形", 9: "四边形",
                10: "四面体", 12: "六面体", 13: "楔形", 14: "金字塔"
            }

            for cell_type in cell_types:
                type_name = type_names.get(cell_type, f"未知类型({cell_type})")
                print(f"  类型 {cell_type}: {type_name}")

                # 检查是否是2D网格（只有面单元）
                if cell_type in [5, 9]:  # 三角形或四边形
                    print("⚠️  检测到2D面网格，流线追踪可能需要3D体网格")

            print("=== 诊断结束 ===")

        except Exception as e:
            print(f"诊断过程失败: {e}")

    def _prepare_grid_for_streamlines(self, ugrid: vtk.vtkUnstructuredGrid) -> Optional[vtk.vtkUnstructuredGrid]:
        """准备适合流线追踪的网格"""
        try:
            print("=== 网格预处理 ===")

            # 检查原始网格类型
            cell_types = set()
            for i in range(min(10, ugrid.GetNumberOfCells())):
                cell_type = ugrid.GetCellType(i)
                cell_types.add(cell_type)

            print(f"原始网格单元类型: {cell_types}")

            # 如果是2D面网格（三角形或四边形），尝试转换
            if all(ct in [5, 9] for ct in cell_types):  # 只有三角形或四边形
                print("检测到2D面网格，尝试转换为适合流线追踪的格式...")

                # 方法1: 尝试使用Delaunay3D创建体网格
                try:
                    # 首先提取点
                    points = ugrid.GetPoints()
                    point_data = ugrid.GetPointData()

                    # 创建点云
                    point_cloud = vtk.vtkPolyData()
                    point_cloud.SetPoints(points)

                    # 使用Delaunay3D创建体网格
                    delaunay = vtk.vtkDelaunay3D()
                    delaunay.SetInputData(point_cloud)
                    delaunay.SetTolerance(0.001)
                    delaunay.Update()

                    volume_grid = delaunay.GetOutput()

                    if volume_grid.GetNumberOfCells() > 0:
                        # 将原始的点数据复制到新网格
                        new_point_data = volume_grid.GetPointData()
                        for i in range(point_data.GetNumberOfArrays()):
                            array = point_data.GetArray(i)
                            if array.GetNumberOfTuples() == volume_grid.GetNumberOfPoints():
                                new_point_data.AddArray(array)
                                if array.GetName() and 'velocity' in array.GetName().lower():
                                    new_point_data.SetVectors(array)

                        print(f"✓ Delaunay3D转换成功: {volume_grid.GetNumberOfPoints()} 个点, {volume_grid.GetNumberOfCells()} 个体单元")
                        return volume_grid
                    else:
                        print("❌ Delaunay3D转换失败：没有生成体单元")

                except Exception as e:
                    print(f"❌ Delaunay3D转换失败: {e}")

                # 方法2: 尝试直接使用面网格（某些情况下可能工作）
                print("尝试直接使用面网格进行流线追踪...")

                # 确保矢量数据正确设置
                vectors = ugrid.GetPointData().GetVectors()
                if vectors is None:
                    # 尝试找到速度数据并设置为矢量
                    point_data = ugrid.GetPointData()
                    for i in range(point_data.GetNumberOfArrays()):
                        array = point_data.GetArray(i)
                        if array.GetName() and 'velocity' in array.GetName().lower():
                            if array.GetNumberOfComponents() >= 3:
                                point_data.SetVectors(array)
                                print(f"✓ 设置矢量数据: {array.GetName()}")
                                break

                return ugrid

            else:
                print("网格已经包含体单元，直接使用")
                return ugrid

        except Exception as e:
            print(f"网格预处理失败: {e}")
            return ugrid

    def create_3d_streamlines_from_inlets(self, prepared_grids: dict,
                                        inlet_names: list = None,
                                        max_points: int = 25,
                                        sampling_method: str = "vertex") -> Optional[vtk.vtkActor]:
        """
        参考CFD软件实现的3D流线创建方法
        从指定入口开始生成流线，穿越所有域

        Args:
            prepared_grids: 预处理的网格字典 {region_name: vtk_grid}
            inlet_names: 入口区域名称列表，如 ['inlet1too', 'inlet2too', 'inlet3too', 'inlet4too']
            max_points: 最大点数限制
            sampling_method: 采样方法 ('vertex', 'uniform', 'random')

        Returns:
            vtk.vtkActor: 3D流线actor或None
        """
        try:
            print("=== 创建3D流线（从入口开始）===")

            # 默认入口名称
            if inlet_names is None:
                inlet_names = ['inlet1too', 'inlet2too', 'inlet3too', 'inlet4too']

            # 1. 合并所有域的网格（已预处理）
            combined_grid = self._combine_all_domains(prepared_grids)
            if combined_grid is None:
                print("❌ 无法合并域网格")
                return None

            # 2. 从入口区域创建种子点（简化策略）
            seed_points = self._create_inlet_seed_points_simple(prepared_grids, inlet_names, max_points)
            if seed_points is None:
                print("❌ 无法创建入口种子点")
                return None

            # 3. 使用合并的网格进行流线追踪（简化版本）
            streamline_actor = self._trace_3d_streamlines_simple(combined_grid, seed_points)

            if streamline_actor:
                print("✓ 3D流线创建成功")
                return streamline_actor
            else:
                print("❌ 3D流线创建失败")
                return None

        except Exception as e:
            print(f"创建3D流线失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _combine_all_domains(self, all_grids: dict) -> Optional[vtk.vtkUnstructuredGrid]:
        """合并所有域的网格"""
        try:
            print(f"合并 {len(all_grids)} 个域的网格...")

            # 使用AppendFilter合并网格
            append_filter = vtk.vtkAppendFilter()

            total_points = 0
            total_cells = 0

            for region_name, grid in all_grids.items():
                if grid and grid.GetNumberOfPoints() > 0:
                    append_filter.AddInputData(grid)
                    total_points += grid.GetNumberOfPoints()
                    total_cells += grid.GetNumberOfCells()
                    print(f"  添加域 {region_name}: {grid.GetNumberOfPoints()} 个点, {grid.GetNumberOfCells()} 个单元")

            append_filter.Update()
            combined_grid = append_filter.GetOutput()

            print(f"✓ 网格合并完成: {combined_grid.GetNumberOfPoints()} 个点, {combined_grid.GetNumberOfCells()} 个单元")
            print(f"  原始总计: {total_points} 个点, {total_cells} 个单元")

            return combined_grid

        except Exception as e:
            print(f"合并网格失败: {e}")
            return None

    def _create_inlet_seed_points_simple(self, prepared_grids: dict, inlet_names: list, max_points: int) -> Optional[vtk.vtkPolyData]:
        """简化的入口种子点创建"""
        try:
            print(f"从入口创建种子点: {inlet_names}")

            all_seed_points = vtk.vtkPoints()
            points_per_inlet = max(1, max_points // len(inlet_names))

            for inlet_name in inlet_names:
                # 直接查找匹配的入口区域
                if inlet_name in prepared_grids:
                    grid = prepared_grids[inlet_name]
                    if grid and grid.GetNumberOfPoints() > 0:
                        # 简单均匀采样
                        total_points = grid.GetNumberOfPoints()
                        step = max(1, total_points // points_per_inlet)

                        for i in range(0, total_points, step):
                            if all_seed_points.GetNumberOfPoints() >= max_points:
                                break
                            point = grid.GetPoint(i)
                            all_seed_points.InsertNextPoint(point)

                        print(f"✓ 入口 {inlet_name}: 添加了 {min(points_per_inlet, total_points)} 个种子点")
                else:
                    print(f"⚠️ 未找到入口区域: {inlet_name}")

            total_seeds = all_seed_points.GetNumberOfPoints()
            print(f"✓ 总共创建了 {total_seeds} 个入口种子点")

            if total_seeds == 0:
                return None

            # 创建种子点的PolyData
            seed_polydata = vtk.vtkPolyData()
            seed_polydata.SetPoints(all_seed_points)

            # 创建顶点
            vertices = vtk.vtkCellArray()
            for i in range(total_seeds):
                vertices.InsertNextCell(1, [i])
            seed_polydata.SetVerts(vertices)

            return seed_polydata

        except Exception as e:
            print(f"创建入口种子点失败: {e}")
            return None

    def _trace_3d_streamlines_simple(self, combined_grid: vtk.vtkUnstructuredGrid, seed_points: vtk.vtkPolyData) -> Optional[vtk.vtkActor]:
        """简化的3D流线追踪"""
        try:
            print("开始简化3D流线追踪...")

            # 创建流线追踪器 - 只使用RK4
            tracer = vtk.vtkStreamTracer()
            tracer.SetInputData(combined_grid)

            # 创建种子源
            seed_source = vtk.vtkProgrammableSource()
            seed_source.SetExecuteMethod(lambda: seed_source.GetPolyDataOutput().ShallowCopy(seed_points))

            tracer.SetSourceConnection(seed_source.GetOutputPort())
            tracer.SetIntegratorTypeToRungeKutta4()

            # 优化的参数设置
            bounds = combined_grid.GetBounds()
            domain_size = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])

            vectors = combined_grid.GetPointData().GetVectors()
            if vectors:
                velocity_range = vectors.GetRange(-1)
                avg_velocity = max(velocity_range[1], 1e-6)
                time_step = domain_size / (avg_velocity * 200)  # 更大的时间步长提高效率
            else:
                time_step = domain_size / 200

            # 设置追踪参数
            tracer.SetMaximumPropagation(domain_size * 50)  # 大传播距离
            tracer.SetInitialIntegrationStep(time_step)
            tracer.SetMaximumIntegrationStep(time_step * 5)
            tracer.SetMinimumIntegrationStep(time_step / 5)
            tracer.SetIntegrationDirectionToBoth()
            tracer.SetMaximumNumberOfSteps(20000)  # 大步数
            tracer.SetTerminalSpeed(1e-12)

            print(f"简化3D流线参数: 域大小={domain_size:.3f}, 时间步长={time_step:.6f}")
            print(f"种子点数: {seed_points.GetNumberOfPoints()}")

            # 执行追踪
            tracer.Update()
            output = tracer.GetOutput()

            num_points = output.GetNumberOfPoints()
            num_lines = output.GetNumberOfLines()

            print(f"简化3D流线结果: {num_points} 个点, {num_lines} 条流线")

            if num_points == 0:
                print("❌ 没有生成3D流线")
                return None

            # 简化渲染 - 直接使用线条
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(output)

            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetColor(1, 0, 0)  # 红色
            actor.GetProperty().SetLineWidth(3)    # 线宽
            actor.GetProperty().SetRepresentationToWireframe()

            print(f"✓ 简化3D流线渲染完成: {num_lines} 条流线")
            return actor

        except Exception as e:
            print(f"简化3D流线追踪失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def create_optimized_streamlines(self, all_grids: Dict[str, vtk.vtkUnstructuredGrid],
                                   selected_regions: List[str] = None,
                                   max_streamlines: int = 20,
                                   use_velocity_based_seeds: bool = True,
                                   global_velocity_range: Tuple[float, float] = None) -> Optional[vtk.vtkActor]:
        """
        创建优化的流线可视化，从入口到出口，在所有三维点集区域内计算

        Args:
            all_grids: 所有区域的VTK网格字典
            selected_regions: 选择的区域列表（用于控制显示）
            max_streamlines: 最大流线数量
            use_velocity_based_seeds: 是否使用基于速度的种子点选择
            global_velocity_range: 全局速度范围

        Returns:
            vtk.vtkActor: 流线actor或None
        """
        try:
            print("=== 创建优化流线可视化（从入口到出口）===")

            # 1. 筛选三维点集区域（面片数为0的RegionData）
            volume_grids = self._filter_volume_regions(all_grids)
            if not volume_grids:
                print("❌ 没有找到三维点集区域")
                return None

            # 2. 合并所有三维点集区域
            combined_grid = self._combine_volume_grids(volume_grids)
            if combined_grid is None:
                print("❌ 无法合并三维点集区域")
                return None

            # 3. 从入口区域创建种子点
            inlet_names = ['inlet1too', 'inlet2too', 'inlet3too', 'inlet4too']
            seed_points = self._create_inlet_seeds_from_grids(all_grids, inlet_names, max_streamlines)
            if seed_points is None:
                print("❌ 无法从入口创建种子点")
                return None

            # 4. 使用合并网格进行流线追踪（从入口到出口）
            streamline_polydata = self._trace_inlet_to_outlet_streamlines(
                combined_grid, seed_points, outlet_name='outlettoo')
            if streamline_polydata is None:
                print("❌ 无法追踪流线")
                return None

            # 5. 根据选择区域过滤流线
            if selected_regions:
                streamline_polydata = self._filter_streamlines_by_regions(
                    streamline_polydata, all_grids, selected_regions)

            # 6. 创建带全局速度颜色映射的actor
            actor = self._create_global_velocity_colored_actor(
                streamline_polydata, combined_grid, global_velocity_range)
            if actor:
                print(f"✓ 优化流线创建成功")
                return actor
            else:
                print("❌ 无法创建流线actor")
                return None

        except Exception as e:
            print(f"创建优化流线失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _filter_volume_regions(self, all_grids: Dict[str, vtk.vtkUnstructuredGrid]) -> Dict[str, vtk.vtkUnstructuredGrid]:
        """筛选三维点集区域（面片数为0的RegionData）"""
        try:
            volume_grids = {}
            for region_name, grid in all_grids.items():
                # 检查是否为三维点集区域
                if grid and grid.GetNumberOfPoints() > 0:
                    # 检查单元类型
                    if grid.GetNumberOfCells() > 0:
                        # 有单元的情况：检查单元类型
                        cell_types = set()
                        for i in range(min(10, grid.GetNumberOfCells())):
                            cell_type = grid.GetCellType(i)
                            cell_types.add(cell_type)

                        # VTK单元类型：
                        # 1: 点单元 (VTK_VERTEX)
                        # 5: 三角形 (VTK_TRIANGLE)
                        # 9: 四边形 (VTK_QUAD)
                        # 10: 四面体 (VTK_TETRA)
                        # 12: 六面体 (VTK_HEXAHEDRON)
                        # 13: 楔形 (VTK_WEDGE)
                        # 14: 金字塔 (VTK_PYRAMID)

                        volume_cell_types = {10, 12, 13, 14}  # 体积单元
                        surface_cell_types = {5, 9}          # 面单元
                        point_cell_types = {1}               # 点单元

                        if cell_types & volume_cell_types:
                            volume_grids[region_name] = grid
                            print(f"✓ 区域 {region_name}: 三维点集区域（体积单元）")
                        elif cell_types <= point_cell_types:
                            # 点单元通常表示三维点集区域（CFX中面片数为0的区域）
                            volume_grids[region_name] = grid
                            print(f"✓ 区域 {region_name}: 三维点集区域（点单元）")
                        elif cell_types <= surface_cell_types:
                            print(f"- 区域 {region_name}: 面单元区域")
                        else:
                            print(f"- 区域 {region_name}: 其他类型区域，单元类型: {cell_types}")
                    else:
                        # 没有单元但有点的情况：很可能是三维点集区域
                        # 这种情况通常对应CFX中面片数为0的RegionData
                        volume_grids[region_name] = grid
                        print(f"✓ 区域 {region_name}: 三维点集区域（纯点数据）")
                else:
                    print(f"- 区域 {region_name}: 无效区域")

            print(f"找到 {len(volume_grids)} 个三维点集区域")
            return volume_grids

        except Exception as e:
            print(f"筛选三维点集区域失败: {e}")
            return {}

    def _combine_volume_grids(self, volume_grids: Dict[str, vtk.vtkUnstructuredGrid]) -> Optional[vtk.vtkUnstructuredGrid]:
        """合并所有三维点集区域"""
        try:
            if not volume_grids:
                return None

            print(f"合并 {len(volume_grids)} 个三维点集区域...")

            # 使用vtkAppendFilter合并网格
            append_filter = vtk.vtkAppendFilter()

            for region_name, grid in volume_grids.items():
                if grid and grid.GetNumberOfPoints() > 0:
                    append_filter.AddInputData(grid)
                    print(f"  添加区域 {region_name}: {grid.GetNumberOfPoints()} 点, {grid.GetNumberOfCells()} 单元")

            append_filter.Update()
            combined_grid = append_filter.GetOutput()

            print(f"✓ 合并完成: {combined_grid.GetNumberOfPoints()} 点, {combined_grid.GetNumberOfCells()} 单元")
            return combined_grid

        except Exception as e:
            print(f"合并三维点集区域失败: {e}")
            return None

    def _create_inlet_seeds_from_grids(self, all_grids: Dict[str, vtk.vtkUnstructuredGrid],
                                     inlet_names: List[str], max_seeds: int) -> Optional[vtk.vtkPolyData]:
        """从入口区域创建智能种子点（基于速度的选择策略）"""
        try:
            print(f"从入口创建智能种子点: {inlet_names}")

            all_seed_points = vtk.vtkPoints()
            seeds_per_inlet = 4  # 每个入口选择4个种子点
            total_seeds = 0

            for inlet_name in inlet_names:
                if inlet_name in all_grids:
                    grid = all_grids[inlet_name]
                    if grid and grid.GetNumberOfPoints() > 0:
                        num_points = grid.GetNumberOfPoints()
                        print(f"入口平面 '{inlet_name}' 点数: {num_points}")

                        # 获取速度数据
                        vectors = grid.GetPointData().GetVectors()
                        if vectors:
                            # 提取速度数据并计算速度大小
                            inlet_velocities = []
                            inlet_points = []

                            for i in range(num_points):
                                point = grid.GetPoint(i)
                                velocity = vectors.GetTuple3(i)
                                velocity_magnitude = np.linalg.norm(velocity)

                                inlet_points.append(point)
                                inlet_velocities.append(velocity_magnitude)

                            inlet_velocities = np.array(inlet_velocities)
                            inlet_points = np.array(inlet_points)

                            print(f"  速度范围: {inlet_velocities.min():.6f} - "
                                  f"{inlet_velocities.max():.6f} m/s")

                            # 智能种子点选择：基于速度大小选择最佳位置
                            threshold_75 = np.percentile(inlet_velocities, 75)
                            high_vel_indices = np.where(inlet_velocities >= threshold_75)[0]

                            print(f"  高速度点数: {len(high_vel_indices)} "
                                  f"(阈值: {threshold_75:.6f})")

                            # 选择种子点
                            selected_indices = []
                            if len(high_vel_indices) >= seeds_per_inlet:
                                # 均匀选择高速度点
                                step = len(high_vel_indices) // seeds_per_inlet
                                selected_indices = high_vel_indices[::step][:seeds_per_inlet]
                            else:
                                # 补充中等速度点
                                selected_indices = high_vel_indices.tolist()
                                threshold_50 = np.percentile(inlet_velocities, 50)
                                med_vel_indices = np.where(inlet_velocities >= threshold_50)[0]
                                remaining = seeds_per_inlet - len(selected_indices)
                                if remaining > 0 and len(med_vel_indices) > 0:
                                    step = max(1, len(med_vel_indices) // remaining)
                                    additional = med_vel_indices[::step][:remaining]
                                    selected_indices.extend(additional)

                            # 添加选中的种子点
                            seeds_added = 0
                            for idx in selected_indices:
                                if idx < len(inlet_points):
                                    point = inlet_points[idx]
                                    all_seed_points.InsertNextPoint(point[0], point[1], point[2])
                                    seeds_added += 1
                                    total_seeds += 1

                            print(f"  为 '{inlet_name}' 添加了 {seeds_added} 个基于速度的种子点")

                        else:
                            # 如果没有速度数据，使用几何中心方法
                            print(f"  警告: '{inlet_name}' 没有速度数据，使用几何中心")

                            # 计算几何中心
                            center = np.zeros(3)
                            for i in range(num_points):
                                point = grid.GetPoint(i)
                                center += np.array(point)
                            center /= num_points

                            all_seed_points.InsertNextPoint(center[0], center[1], center[2])
                            total_seeds += 1
                            print("  添加几何中心种子点")

                else:
                    print(f"  ⚠️ 未找到入口区域: {inlet_name}")

            print(f"✓ 总共创建 {total_seeds} 个智能入口种子点")

            if total_seeds == 0:
                return None

            # 创建种子点的PolyData
            seed_polydata = vtk.vtkPolyData()
            seed_polydata.SetPoints(all_seed_points)

            return seed_polydata

        except Exception as e:
            print(f"从入口创建智能种子点失败: {e}")
            return None

    def _trace_inlet_to_outlet_streamlines(self, combined_grid: vtk.vtkUnstructuredGrid,
                                         seed_points: vtk.vtkPolyData,
                                         outlet_name: str = 'outlettoo') -> Optional[vtk.vtkPolyData]:
        """从入口到出口追踪流线（优化版本）"""
        try:
            print("追踪从入口到出口的流线（使用优化算法）...")

            # 检查速度数据
            vectors = combined_grid.GetPointData().GetVectors()
            if vectors is None:
                print("❌ 合并网格中没有速度数据")
                return None

            # 准备数据用于优化追踪
            grid_points = []
            grid_velocities = []
            for i in range(combined_grid.GetNumberOfPoints()):
                point = combined_grid.GetPoint(i)
                velocity = vectors.GetTuple3(i)
                grid_points.append(point)
                grid_velocities.append(velocity)

            grid_points = np.array(grid_points, dtype=np.float32)
            grid_velocities = np.array(grid_velocities, dtype=np.float32)

            # 建立KDTree用于快速速度插值
            kdtree = cKDTree(grid_points)

            # 预计算边界
            bounds = combined_grid.GetBounds()
            bounds_dict = {
                'x_min': bounds[0], 'x_max': bounds[1],
                'y_min': bounds[2], 'y_max': bounds[3],
                'z_min': bounds[4], 'z_max': bounds[5]
            }

            # 提取种子点
            seed_points_list = []
            for i in range(seed_points.GetNumberOfPoints()):
                seed_points_list.append(seed_points.GetPoint(i))

            print(f"开始追踪 {len(seed_points_list)} 条流线...")

            # 使用优化的流线追踪方法
            all_streamlines = []
            for seed_point in seed_points_list:
                streamline = self._trace_single_streamline_optimized(
                    seed_point, kdtree, grid_points, grid_velocities, bounds_dict)
                if len(streamline) > 3:  # 只保留有效流线
                    all_streamlines.append(streamline)

            if not all_streamlines:
                print("❌ 没有生成有效流线")
                return None

            # 构建VTK流线数据
            streamline_polydata = self._build_streamline_polydata(all_streamlines)

            print(f"✓ 优化流线追踪完成: {len(all_streamlines)} 条流线")
            return streamline_polydata

        except Exception as e:
            print(f"追踪流线失败: {e}")
            return None

    def _filter_streamlines_by_regions(self, streamlines: vtk.vtkPolyData,
                                     all_grids: Dict[str, vtk.vtkUnstructuredGrid],
                                     selected_regions: List[str]) -> vtk.vtkPolyData:
        """根据选择区域过滤流线"""
        try:
            print(f"根据选择区域过滤流线: {selected_regions}")

            # 简化实现：如果选择了区域，返回原流线
            # 更复杂的实现需要检查流线点是否在选择区域内
            # 这里先返回原流线，后续可以优化
            print("✓ 流线过滤完成（简化实现）")
            return streamlines

        except Exception as e:
            print(f"过滤流线失败: {e}")
            return streamlines

    def _create_global_velocity_colored_actor(self, streamlines: vtk.vtkPolyData,
                                            combined_grid: vtk.vtkUnstructuredGrid,
                                            global_velocity_range: Tuple[float, float] = None) -> Optional[vtk.vtkActor]:
        """创建带全局速度颜色映射的流线actor"""
        try:
            if streamlines.GetNumberOfPoints() == 0:
                print("❌ 没有流线点数据")
                return None

            # 为流线点添加速度大小数据
            self._add_velocity_magnitude_to_streamlines(streamlines, combined_grid)

            # 创建mapper
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(streamlines)
            mapper.SetScalarModeToUsePointData()
            mapper.SelectColorArray("VelocityMagnitude")

            # 使用全局速度范围或计算范围
            if global_velocity_range:
                min_vel, max_vel = global_velocity_range
                print(f"使用全局速度范围: {min_vel:.3f} - {max_vel:.3f} m/s")
            else:
                # 计算当前流线的速度范围
                velocity_array = streamlines.GetPointData().GetArray("VelocityMagnitude")
                if velocity_array:
                    min_vel, max_vel = velocity_array.GetRange()
                    print(f"计算流线速度范围: {min_vel:.3f} - {max_vel:.3f} m/s")
                else:
                    print("❌ 无法获取速度数据")
                    return None

            # 创建彩虹色谱查找表（蓝色到红色）
            lut = vtk.vtkLookupTable()
            lut.SetNumberOfTableValues(256)
            lut.SetHueRange(0.667, 0.0)  # 蓝色到红色
            lut.SetSaturationRange(1.0, 1.0)
            lut.SetValueRange(1.0, 1.0)
            lut.SetAlphaRange(1.0, 1.0)
            lut.SetRange(min_vel, max_vel)  # 设置范围
            lut.Build()

            # 存储颜色查找表和速度范围供颜色条使用
            self.velocity_lookup_table = lut
            self.velocity_range = (min_vel, max_vel)

            mapper.SetLookupTable(lut)
            mapper.SetScalarRange(min_vel, max_vel)

            # 创建actor
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetLineWidth(2)

            print(f"✓ 创建全局速度颜色流线actor: 速度范围 {min_vel:.3f} - {max_vel:.3f} m/s")
            return actor

        except Exception as e:
            print(f"创建全局速度颜色actor失败: {e}")
            return None

    def _add_velocity_magnitude_to_streamlines(self, streamlines: vtk.vtkPolyData,
                                             combined_grid: vtk.vtkUnstructuredGrid):
        """为流线点添加速度大小数据（优化版本）"""
        try:
            # 获取合并网格的速度数据
            vectors = combined_grid.GetPointData().GetVectors()
            if vectors is None:
                print("❌ 合并网格中没有速度数据")
                return

            # 提取合并网格的点坐标和速度数据
            grid_points = []
            grid_velocities = []
            for i in range(combined_grid.GetNumberOfPoints()):
                point = combined_grid.GetPoint(i)
                velocity = vectors.GetTuple3(i)
                magnitude = np.linalg.norm(velocity)
                grid_points.append(point)
                grid_velocities.append(magnitude)

            grid_points = np.array(grid_points)
            grid_velocities = np.array(grid_velocities)

            # 建立KDTree
            kdtree = cKDTree(grid_points)

            # 批量提取流线点坐标
            num_points = streamlines.GetNumberOfPoints()
            streamline_points = np.zeros((num_points, 3))
            for i in range(num_points):
                point = streamlines.GetPoint(i)
                streamline_points[i] = point

            # 批量查询最近邻点
            _, indices = kdtree.query(streamline_points, k=1)
            point_velocity_magnitudes = grid_velocities[indices.flatten()]

            # 创建速度大小数组并批量添加数据
            velocity_magnitudes = vtk.vtkFloatArray()
            velocity_magnitudes.SetNumberOfComponents(1)
            velocity_magnitudes.SetName("VelocityMagnitude")

            for magnitude in point_velocity_magnitudes:
                velocity_magnitudes.InsertNextValue(magnitude)

            # 将速度大小数据添加到流线
            streamlines.GetPointData().SetScalars(velocity_magnitudes)
            streamlines.GetPointData().SetActiveScalars("VelocityMagnitude")

            print(f"✓ 批量为 {num_points} 个流线点添加了速度大小数据")

        except Exception as e:
            print(f"添加速度大小数据失败: {e}")

    def create_velocity_color_bar(self, position: Tuple[float, float] = (0.85, 0.1)) -> Optional[vtk.vtkScalarBarActor]:
        """创建速度颜色条"""
        try:
            if not hasattr(self, 'velocity_lookup_table') or not hasattr(self, 'velocity_range'):
                print("❌ 没有速度颜色查找表数据")
                return None

            # 创建颜色条
            scalar_bar = vtk.vtkScalarBarActor()
            scalar_bar.SetLookupTable(self.velocity_lookup_table)
            scalar_bar.SetTitle("速度大小 (m/s)")
            scalar_bar.SetNumberOfLabels(5)

            # 设置颜色条位置和大小
            scalar_bar.SetPosition(position[0], position[1])  # 使用传入的位置
            scalar_bar.SetWidth(0.1)           # 宽度
            scalar_bar.SetHeight(0.8)          # 高度

            # 设置标题属性
            title_prop = scalar_bar.GetTitleTextProperty()
            title_prop.SetFontSize(12)
            title_prop.SetColor(0, 0, 0)  # 黑色
            title_prop.SetBold(True)

            # 设置标签属性
            label_prop = scalar_bar.GetLabelTextProperty()
            label_prop.SetFontSize(10)
            label_prop.SetColor(0, 0, 0)  # 黑色

            # 设置颜色条样式
            scalar_bar.SetMaximumNumberOfColors(256)
            scalar_bar.SetOrientationToVertical()

            min_vel, max_vel = self.velocity_range
            print(f"✓ 创建速度颜色条: {min_vel:.3f} - {max_vel:.3f} m/s")

            return scalar_bar

        except Exception as e:
            print(f"创建速度颜色条失败: {e}")
            return None

    def has_velocity_color_data(self) -> bool:
        """检查是否有速度颜色数据"""
        return (hasattr(self, 'velocity_lookup_table') and
                hasattr(self, 'velocity_range') and
                self.velocity_lookup_table is not None)

    def _trace_single_streamline_optimized(self, seed_point, kdtree, grid_points,
                                         grid_velocities, bounds_dict):
        """优化的单条流线追踪方法"""
        current_point = np.array(seed_point, dtype=np.float32)
        line_points = [current_point.copy()]

        # 检测初始速度以确定参数
        initial_velocity = self._get_velocity_at_point_optimized(
            current_point, kdtree, grid_velocities)
        initial_speed = np.linalg.norm(initial_velocity)

        # 根据初始速度调整参数
        if initial_speed < 0.2:  # 低速度区域
            max_steps = 20000
            base_step_size = 0.002
            min_velocity = 1e-12
        else:  # 正常速度区域
            max_steps = 10000
            base_step_size = 0.0005
            min_velocity = 1e-10

        for step in range(max_steps):
            # 获取当前点的速度
            velocity = self._get_velocity_at_point_optimized(
                current_point, kdtree, grid_velocities)
            velocity_magnitude = np.linalg.norm(velocity)

            # 检查速度是否有效
            if velocity_magnitude < min_velocity:
                break

            # 自适应步长
            if initial_speed < 0.2:
                adaptive_step = base_step_size * min(5.0, max(1.0, velocity_magnitude / 0.001))
            else:
                adaptive_step = base_step_size * min(3.0, max(0.1, velocity_magnitude / 0.01))

            next_point = current_point + adaptive_step * velocity

            # 边界检查
            margin = 0.01 if initial_speed < 0.2 else 0.0
            if (next_point[0] < bounds_dict['x_min'] - margin or
                next_point[0] > bounds_dict['x_max'] + margin or
                next_point[1] < bounds_dict['y_min'] - margin or
                next_point[1] > bounds_dict['y_max'] + margin or
                next_point[2] < bounds_dict['z_min'] - margin or
                next_point[2] > bounds_dict['z_max'] + margin):
                break

            # 循环检测
            if len(line_points) > 30 and step % 10 == 0:
                recent_points = np.array(line_points[-3:])
                distances = np.linalg.norm(recent_points - next_point, axis=1)
                if distances.min() < adaptive_step * 0.1:
                    break

            line_points.append(next_point.copy())
            current_point = next_point

        return line_points

    def _get_velocity_at_point_optimized(self, point, kdtree, grid_velocities):
        """优化的速度插值方法"""
        # 查询最近的6个点进行插值
        distances, indices = kdtree.query(point, k=6)
        distances = np.maximum(distances, 1e-12)

        # 如果非常接近某个点，直接返回
        if distances[0] < 1e-10:
            return grid_velocities[indices[0]]

        # 使用高斯权重插值
        sigma = distances.mean() * 0.5
        weights = np.exp(-(distances**2) / (2 * sigma**2))
        weights /= weights.sum()

        # 计算插值速度
        velocity = np.sum(weights.reshape(-1, 1) * grid_velocities[indices], axis=0)

        # 速度限制
        velocity_mag = np.linalg.norm(velocity)
        if velocity_mag > 0.8:
            velocity *= 0.8 / velocity_mag

        return velocity

    def _build_streamline_polydata(self, all_streamlines):
        """构建VTK流线数据"""
        flow_direction = vtk.vtkPolyData()
        flow_points = vtk.vtkPoints()
        flow_lines = vtk.vtkCellArray()

        point_id = 0
        for line_points in all_streamlines:
            line_start_id = point_id
            for point in line_points:
                flow_points.InsertNextPoint(point[0], point[1], point[2])
                point_id += 1

            # 创建线段连接
            for k in range(len(line_points) - 1):
                flow_lines.InsertNextCell(2)
                flow_lines.InsertCellPoint(line_start_id + k)
                flow_lines.InsertCellPoint(line_start_id + k + 1)

        flow_direction.SetPoints(flow_points)
        flow_direction.SetLines(flow_lines)

        return flow_direction

    def _extract_points_from_grid(self, ugrid: vtk.vtkUnstructuredGrid) -> Optional[np.ndarray]:
        """从VTK网格中提取点坐标"""
        try:
            num_points = ugrid.GetNumberOfPoints()
            points_array = np.zeros((num_points, 3))

            for i in range(num_points):
                point = ugrid.GetPoint(i)
                points_array[i] = point

            return points_array
        except Exception as e:
            print(f"提取点坐标失败: {e}")
            return None

    def _extract_velocity_from_grid(self, ugrid: vtk.vtkUnstructuredGrid) -> Optional[np.ndarray]:
        """从VTK网格中提取速度数据"""
        try:
            vectors = ugrid.GetPointData().GetVectors()
            if vectors is None:
                return None

            num_points = vectors.GetNumberOfTuples()
            velocity_array = np.zeros((num_points, 3))

            for i in range(num_points):
                velocity_array[i] = vectors.GetTuple3(i)

            return velocity_array
        except Exception as e:
            print(f"提取速度数据失败: {e}")
            return None

    def _create_velocity_based_seeds(self, points_array: np.ndarray,
                                   max_seeds: int) -> Optional[List[np.ndarray]]:
        """基于速度大小创建智能种子点"""
        try:
            # 选择高速度区域的点作为种子
            threshold_75 = np.percentile(self.velocity_magnitudes, 75)
            high_vel_indices = np.where(self.velocity_magnitudes >= threshold_75)[0]

            print(f"高速度点数: {len(high_vel_indices)} (阈值: {threshold_75:.6f})")

            if len(high_vel_indices) == 0:
                # 如果没有高速度点，使用中等速度点
                threshold_50 = np.percentile(self.velocity_magnitudes, 50)
                high_vel_indices = np.where(self.velocity_magnitudes >= threshold_50)[0]

            if len(high_vel_indices) == 0:
                print("❌ 没有找到合适的种子点")
                return None

            # 均匀选择种子点
            if len(high_vel_indices) >= max_seeds:
                step = len(high_vel_indices) // max_seeds
                selected_indices = high_vel_indices[::step][:max_seeds]
            else:
                selected_indices = high_vel_indices

            seed_points = [points_array[idx] for idx in selected_indices]
            print(f"创建了 {len(seed_points)} 个基于速度的种子点")

            return seed_points

        except Exception as e:
            print(f"创建速度种子点失败: {e}")
            return None

    def _create_uniform_seeds(self, ugrid: vtk.vtkUnstructuredGrid,
                            max_seeds: int) -> Optional[List[np.ndarray]]:
        """创建均匀分布的种子点"""
        try:
            bounds = ugrid.GetBounds()
            center_x = (bounds[0] + bounds[1]) / 2
            center_y = (bounds[2] + bounds[3]) / 2
            center_z = (bounds[4] + bounds[5]) / 2

            # 在中心区域创建种子点
            radius = min(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4]) / 8

            seed_points = []
            for i in range(max_seeds):
                # 在球形区域内随机分布
                theta = np.random.uniform(0, 2 * np.pi)
                phi = np.random.uniform(0, np.pi)
                r = np.random.uniform(0, radius)

                x = center_x + r * np.sin(phi) * np.cos(theta)
                y = center_y + r * np.sin(phi) * np.sin(theta)
                z = center_z + r * np.cos(phi)

                seed_points.append(np.array([x, y, z]))

            print(f"创建了 {len(seed_points)} 个均匀分布种子点")
            return seed_points

        except Exception as e:
            print(f"创建均匀种子点失败: {e}")
            return None

    def _get_velocity_at_point(self, point: np.ndarray) -> np.ndarray:
        """获取指定点的速度（使用插值）"""
        try:
            # 查找最近的6个点进行插值
            distances, indices = self.kdtree.query(point, k=6)
            distances = np.maximum(distances, 1e-12)

            # 如果非常接近某个点，直接返回
            if distances[0] < 1e-10:
                return self.velocity_data[indices[0]]

            # 使用高斯权重插值
            sigma = distances.mean() * 0.5
            weights = np.exp(-(distances**2) / (2 * sigma**2))
            weights /= weights.sum()

            # 计算插值速度
            velocity = np.sum(weights.reshape(-1, 1) * self.velocity_data[indices], axis=0)

            # 限制速度大小
            velocity_mag = np.linalg.norm(velocity)
            if velocity_mag > 0.8:
                velocity *= 0.8 / velocity_mag

            return velocity

        except Exception as e:
            print(f"速度插值失败: {e}")
            return np.zeros(3)

    def _trace_optimized_streamlines(self, seed_points: List[np.ndarray],
                                   points_array: np.ndarray) -> List[List[np.ndarray]]:
        """使用优化算法追踪流线"""
        try:
            # 计算边界
            bounds = {
                'x_min': points_array[:, 0].min(),
                'x_max': points_array[:, 0].max(),
                'y_min': points_array[:, 1].min(),
                'y_max': points_array[:, 1].max(),
                'z_min': points_array[:, 2].min(),
                'z_max': points_array[:, 2].max()
            }

            def trace_single_streamline(seed_point):
                """追踪单条流线"""
                current_point = np.array(seed_point)
                line_points = [current_point.copy()]

                # 检测初始速度
                initial_velocity = self._get_velocity_at_point(current_point)
                initial_speed = np.linalg.norm(initial_velocity)
                is_low_speed = initial_speed < 0.2

                # 根据速度调整参数
                if is_low_speed:
                    max_steps = 20000
                    base_step_size = 0.002
                    min_velocity = 1e-12
                else:
                    max_steps = 10000
                    base_step_size = 0.0005
                    min_velocity = 1e-10

                for step in range(max_steps):
                    velocity = self._get_velocity_at_point(current_point)
                    velocity_magnitude = np.linalg.norm(velocity)

                    if velocity_magnitude < min_velocity:
                        break

                    # 自适应步长
                    if is_low_speed:
                        adaptive_step = base_step_size * min(5.0, max(1.0, velocity_magnitude / 0.001))
                    else:
                        adaptive_step = base_step_size * min(3.0, max(0.1, velocity_magnitude / 0.01))

                    next_point = current_point + adaptive_step * velocity

                    # 边界检查
                    margin = 0.01 if is_low_speed else 0.0
                    if (next_point[0] < bounds['x_min'] - margin or
                        next_point[0] > bounds['x_max'] + margin or
                        next_point[1] < bounds['y_min'] - margin or
                        next_point[1] > bounds['y_max'] + margin or
                        next_point[2] < bounds['z_min'] - margin or
                        next_point[2] > bounds['z_max'] + margin):
                        stop_reason = "boundary_exceeded"
                        break

                    # 循环检测
                        # 针对低速度区域放宽循环检测
                        if is_low_speed:
                            # 低速度区域：更宽松的循环检测
                            if len(line_points) > 100 and step % 20 == 0:  # 更少检查频率
                                recent_points = np.array(line_points[-5:])  # 检查更多历史点
                                distances = np.linalg.norm(recent_points - next_point,
                                                           axis=1)
                                if distances.min() < adaptive_step * 0.05:  # 更严格的距离阈值
                                    stop_reason = "loop_detected"
                                    break
                        else:
                            # 正常区域：原有的循环检测
                            if len(line_points) > 30 and step % 5 == 0:
                                recent_points = np.array(line_points[-2:])
                                distances = np.linalg.norm(recent_points - next_point,
                                                           axis=1)
                                if distances.min() < adaptive_step * 0.2:
                                    stop_reason = "loop_detected"
                                    break

                    line_points.append(next_point.copy())
                    current_point = next_point

                if is_low_speed and len(line_points) < 100:
                    print(f"    低速度流线停止: {len(line_points)}点, 原因: {stop_reason}")
                return line_points

            # 并行处理流线追踪
            print(f"开始追踪 {len(seed_points)} 条流线...")
            with ThreadPoolExecutor(max_workers=min(4, len(seed_points))) as executor:
                all_streamlines = list(executor.map(trace_single_streamline, seed_points))

            # 过滤有效流线
            valid_streamlines = [line for line in all_streamlines if len(line) > 3]
            print(f"生成了 {len(valid_streamlines)} 条有效流线")

            return valid_streamlines

        except Exception as e:
            print(f"流线追踪失败: {e}")
            return []

    def _create_vtk_streamlines(self, streamlines: List[List[np.ndarray]]) -> vtk.vtkPolyData:
        """将流线数据转换为VTK格式"""
        try:
            flow_direction = vtk.vtkPolyData()
            flow_points = vtk.vtkPoints()
            flow_lines = vtk.vtkCellArray()

            point_id = 0
            for line_points in streamlines:
                if len(line_points) > 3:
                    line_start_id = point_id
                    for point in line_points:
                        flow_points.InsertNextPoint(point[0], point[1], point[2])
                        point_id += 1

                    # 创建线段连接
                    for k in range(len(line_points) - 1):
                        flow_lines.InsertNextCell(2)
                        flow_lines.InsertCellPoint(line_start_id + k)
                        flow_lines.InsertCellPoint(line_start_id + k + 1)

            flow_direction.SetPoints(flow_points)
            flow_direction.SetLines(flow_lines)

            print(f"创建VTK流线: {flow_lines.GetNumberOfCells()} 条线段, {point_id} 个点")
            return flow_direction

        except Exception as e:
            print(f"创建VTK流线失败: {e}")
            return vtk.vtkPolyData()

    def _create_colored_streamline_actor(self, streamlines: vtk.vtkPolyData) -> Optional[vtk.vtkActor]:
        """创建带颜色映射的流线actor"""
        try:
            if streamlines.GetNumberOfPoints() == 0:
                print("❌ 没有流线点数据")
                return None

            # 计算每个点的速度大小
            colors = vtk.vtkFloatArray()
            colors.SetNumberOfComponents(1)
            colors.SetName("VelocityMagnitude")

            # 批量计算速度大小
            points_array = np.zeros((streamlines.GetNumberOfPoints(), 3))
            for i in range(streamlines.GetNumberOfPoints()):
                point = streamlines.GetPoint(i)
                points_array[i] = point

            # 批量查询最近邻点
            _, indices = self.kdtree.query(points_array, k=1)
            point_velocity_magnitudes = self.velocity_magnitudes[indices.flatten()]

            # 添加颜色数据
            for magnitude in point_velocity_magnitudes:
                colors.InsertNextValue(magnitude)

            streamlines.GetPointData().SetScalars(colors)

            # 创建mapper
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(streamlines)
            mapper.SetScalarModeToUsePointData()
            mapper.ScalarVisibilityOn()
            mapper.SelectColorArray("VelocityMagnitude")

            # 设置颜色范围
            min_mag = 0.0
            max_mag = min(0.82, self.velocity_magnitudes.max())
            mapper.SetScalarRange(min_mag, max_mag)

            # 创建彩虹色谱查找表（蓝色到红色）
            lut = vtk.vtkLookupTable()
            lut.SetNumberOfTableValues(256)
            lut.SetHueRange(0.667, 0.0)  # 蓝色到红色
            lut.SetSaturationRange(1.0, 1.0)
            lut.SetValueRange(1.0, 1.0)
            lut.SetAlphaRange(1.0, 1.0)
            lut.SetRange(min_mag, max_mag)  # 设置范围
            lut.Build()

            mapper.SetLookupTable(lut)

            # 创建actor
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetLineWidth(3)
            actor.GetProperty().SetOpacity(0.9)

            print(f"✓ 创建彩色流线actor: 速度范围 {min_mag:.3f} - {max_mag:.3f} m/s")
            return actor

        except Exception as e:
            print(f"创建彩色流线actor失败: {e}")
            return None

    def _create_inlet_seed_points(self, all_grids: dict, inlet_names: list,
                                max_points: int, sampling_method: str) -> Optional[vtk.vtkPolyData]:
        """从入口区域创建种子点"""
        try:
            print(f"从入口创建种子点: {inlet_names}")

            all_seed_points = vtk.vtkPoints()
            points_per_inlet = max(1, max_points // len(inlet_names))

            for inlet_name in inlet_names:
                # 查找匹配的入口区域
                matching_grids = []
                for region_name, grid in all_grids.items():
                    if inlet_name.lower() in region_name.lower():
                        matching_grids.append((region_name, grid))

                if not matching_grids:
                    print(f"⚠️  未找到入口区域: {inlet_name}")
                    continue

                print(f"入口 {inlet_name} 匹配的区域: {[name for name, _ in matching_grids]}")

                # 从匹配的区域中采样点
                for region_name, grid in matching_grids:
                    if grid and grid.GetNumberOfPoints() > 0:
                        inlet_points = self._sample_points_from_grid(grid, points_per_inlet, sampling_method)

                        for i in range(inlet_points.GetNumberOfPoints()):
                            point = inlet_points.GetPoint(i)
                            all_seed_points.InsertNextPoint(point)

            total_seeds = all_seed_points.GetNumberOfPoints()
            print(f"✓ 创建了 {total_seeds} 个入口种子点")

            if total_seeds == 0:
                return None

            # 创建种子点的PolyData
            seed_polydata = vtk.vtkPolyData()
            seed_polydata.SetPoints(all_seed_points)

            # 创建顶点
            vertices = vtk.vtkCellArray()
            for i in range(total_seeds):
                vertices.InsertNextCell(1, [i])
            seed_polydata.SetVerts(vertices)

            return seed_polydata

        except Exception as e:
            print(f"创建入口种子点失败: {e}")
            return None

    def _sample_points_from_grid(self, grid: vtk.vtkUnstructuredGrid,
                               num_points: int, method: str) -> vtk.vtkPoints:
        """从网格中采样点"""
        try:
            points = vtk.vtkPoints()
            total_points = grid.GetNumberOfPoints()

            if total_points == 0:
                return points

            if method == "vertex":
                # 顶点采样：均匀选择顶点
                step = max(1, total_points // num_points)
                for i in range(0, total_points, step):
                    if points.GetNumberOfPoints() >= num_points:
                        break
                    point = grid.GetPoint(i)
                    points.InsertNextPoint(point)

            elif method == "uniform":
                # 均匀采样：在网格边界内均匀分布
                bounds = grid.GetBounds()
                for i in range(num_points):
                    # 在边界内随机选择点，但尽量均匀分布
                    x = bounds[0] + (bounds[1] - bounds[0]) * (i + 0.5) / num_points
                    y = (bounds[2] + bounds[3]) / 2
                    z = (bounds[4] + bounds[5]) / 2
                    points.InsertNextPoint(x, y, z)

            elif method == "random":
                # 随机采样
                import random
                indices = random.sample(range(total_points), min(num_points, total_points))
                for idx in indices:
                    point = grid.GetPoint(idx)
                    points.InsertNextPoint(point)

            return points

        except Exception as e:
            print(f"采样点失败: {e}")
            return vtk.vtkPoints()

    def _trace_3d_streamlines(self, combined_grid: vtk.vtkUnstructuredGrid,
                            seed_points: vtk.vtkPolyData, max_points: int) -> Optional[vtk.vtkActor]:
        """在合并的网格上追踪3D流线"""
        try:
            print("开始3D流线追踪...")

            # 预处理网格
            processed_grid = self._prepare_grid_for_streamlines(combined_grid)
            if processed_grid is None:
                return None

            # 创建流线追踪器
            tracer = vtk.vtkStreamTracer()
            tracer.SetInputData(processed_grid)

            # 创建种子源
            seed_source = vtk.vtkProgrammableSource()
            seed_source.SetExecuteMethod(lambda: seed_source.GetPolyDataOutput().ShallowCopy(seed_points))

            tracer.SetSourceConnection(seed_source.GetOutputPort())
            tracer.SetIntegratorTypeToRungeKutta4()

            # 设置追踪参数（参考CFD软件的设置）
            bounds = processed_grid.GetBounds()
            domain_size = max(bounds[1] - bounds[0], bounds[3] - bounds[2], bounds[5] - bounds[4])

            # 获取速度信息
            vectors = processed_grid.GetPointData().GetVectors()
            if vectors:
                velocity_range = vectors.GetRange(-1)
                avg_velocity = max(velocity_range[1], 1e-6)
                time_step = domain_size / (avg_velocity * 500)  # 适中的时间步长
            else:
                time_step = domain_size / 500

            # 设置追踪参数
            tracer.SetMaximumPropagation(domain_size * 50)  # 大的传播距离，确保能穿越整个域
            tracer.SetInitialIntegrationStep(time_step)
            tracer.SetMaximumIntegrationStep(time_step * 2)
            tracer.SetMinimumIntegrationStep(time_step / 10)
            tracer.SetIntegrationDirectionToBoth()  # 双向追踪
            tracer.SetMaximumNumberOfSteps(20000)  # 大量步数
            tracer.SetTerminalSpeed(1e-12)

            print(f"3D流线参数: 域大小={domain_size:.3f}, 时间步长={time_step:.6f}")
            print(f"种子点数: {seed_points.GetNumberOfPoints()}")

            # 执行追踪
            tracer.Update()
            output = tracer.GetOutput()

            num_points = output.GetNumberOfPoints()
            num_lines = output.GetNumberOfLines()

            print(f"3D流线结果: {num_points} 个点, {num_lines} 条流线")

            if num_points == 0:
                print("❌ 没有生成3D流线")
                self._diagnose_streamline_failure(tracer, processed_grid, seed_source)
                return None

            # 分析流线质量
            self._analyze_streamlines(output)

            # 创建渲染
            return self._render_3d_streamlines(output, domain_size)

        except Exception as e:
            print(f"3D流线追踪失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _render_3d_streamlines(self, streamline_output: vtk.vtkPolyData,
                             domain_size: float) -> vtk.vtkActor:
        """渲染3D流线"""
        try:
            # 使用线条渲染以获得更好的性能和视觉效果
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(streamline_output)

            actor = vtk.vtkActor()
            actor.SetMapper(mapper)

            # 设置流线外观
            actor.GetProperty().SetColor(1, 0, 0)  # 红色
            actor.GetProperty().SetLineWidth(2)    # 线宽
            actor.GetProperty().SetRepresentationToWireframe()

            # 如果流线较少，可以使用管道渲染
            num_lines = streamline_output.GetNumberOfLines()
            if num_lines <= 10:
                tube = vtk.vtkTubeFilter()
                tube.SetInputData(streamline_output)
                tube.SetRadius(domain_size / 500)
                tube.SetNumberOfSides(6)

                tube_mapper = vtk.vtkPolyDataMapper()
                tube_mapper.SetInputConnection(tube.GetOutputPort())

                actor.SetMapper(tube_mapper)
                actor.GetProperty().SetRepresentationToSurface()

                print(f"使用管道渲染 {num_lines} 条3D流线")
            else:
                print(f"使用线条渲染 {num_lines} 条3D流线")

            return actor

        except Exception as e:
            print(f"渲染3D流线失败: {e}")
            # 回退到简单渲染
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputData(streamline_output)

            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetColor(1, 0, 0)
            actor.GetProperty().SetLineWidth(2)

            return actor
