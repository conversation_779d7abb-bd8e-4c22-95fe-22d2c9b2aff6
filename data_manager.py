#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理器
负责管理和缓存CFX数据，提供统一的数据访问接口
"""

from typing import Dict, List, Optional, Tuple, Any
from data_models import RegionData
from cfx_data_parser import CFXDataParser, ProgressCallback


class DataManager:
    """CFX数据管理器"""
    
    def __init__(self):
        self.mesh_data: Dict[str, RegionData] = {}
        self.steady_data: Dict[str, RegionData] = {}
        self.transient_data: Dict[str, RegionData] = {}
        self.parser = CFXDataParser()
        
    def load_mesh_data(self, filename: str, progress_callback: Optional[ProgressCallback] = None) -> bool:
        """
        加载网格数据
        
        Args:
            filename: 文件路径
            progress_callback: 进度回调
            
        Returns:
            bool: 是否加载成功
        """
        try:
            self.mesh_data = self.parser.parse_file(filename, progress_callback)
            print(f"网格数据加载成功: {len(self.mesh_data)} 个区域")
            return True
        except Exception as e:
            print(f"网格数据加载失败: {e}")
            return False
            
    def load_steady_data(self, filename: str, progress_callback: Optional[ProgressCallback] = None) -> bool:
        """
        加载稳态数据

        Args:
            filename: 文件路径
            progress_callback: 进度回调

        Returns:
            bool: 是否加载成功
        """
        try:
            self.steady_data = self.parser.parse_file(filename, progress_callback)
            print(f"稳态数据加载成功: {len(self.steady_data)} 个区域")

            # # 预处理为3D流线追踪格式
            # self._prepare_3d_streamline_data("steady", progress_callback)

            return True
        except Exception as e:
            print(f"稳态数据加载失败: {e}")
            return False
            
    def load_transient_data(self, filename: str, progress_callback: Optional[ProgressCallback] = None) -> bool:
        """
        加载瞬态数据

        Args:
            filename: 文件路径
            progress_callback: 进度回调

        Returns:
            bool: 是否加载成功
        """
        try:
            self.transient_data = self.parser.parse_file(filename, progress_callback)
            print(f"瞬态数据加载成功: {len(self.transient_data)} 个区域")

            # # 预处理为3D流线追踪格式
            # self._prepare_3d_streamline_data("transient", progress_callback)

            return True
        except Exception as e:
            print(f"瞬态数据加载失败: {e}")
            return False
            
    def get_all_region_names(self) -> List[str]:
        """获取所有区域名称"""
        all_regions = set()
        all_regions.update(self.mesh_data.keys())
        all_regions.update(self.steady_data.keys())
        all_regions.update(self.transient_data.keys())
        return sorted(list(all_regions))
        
    def get_mesh_regions(self) -> List[str]:
        """获取网格数据区域名称"""
        return list(self.mesh_data.keys())

    def get_all_mesh_regions(self) -> List[str]:
        """获取所有可用于网格渲染的区域名称（包括稳态和瞬态数据）"""
        all_regions = set()
        all_regions.update(self.mesh_data.keys())
        all_regions.update(self.steady_data.keys())
        all_regions.update(self.transient_data.keys())
        return sorted(list(all_regions))
        
    def get_steady_regions(self) -> List[str]:
        """获取稳态数据区域名称"""
        return list(self.steady_data.keys())
        
    def get_transient_regions(self) -> List[str]:
        """获取瞬态数据区域名称"""
        return list(self.transient_data.keys())
        
    def get_mesh_region(self, region_name: str) -> Optional[RegionData]:
        """获取指定区域的网格数据"""
        return self.mesh_data.get(region_name)

    def get_any_mesh_region(self, region_name: str) -> Optional[RegionData]:
        """
        获取指定区域的网格数据（从任何可用数据源）
        优先级：mesh_data > steady_data > transient_data
        """
        # 首先尝试从专门的网格数据中获取
        if region_name in self.mesh_data:
            return self.mesh_data[region_name]

        # 然后尝试从稳态数据中获取
        if region_name in self.steady_data:
            return self.steady_data[region_name]

        # 最后尝试从瞬态数据中获取
        if region_name in self.transient_data:
            return self.transient_data[region_name]

        return None
        
    def get_steady_region(self, region_name: str) -> Optional[RegionData]:
        """获取指定区域的稳态数据"""
        return self.steady_data.get(region_name)
        
    def get_transient_region(self, region_name: str) -> Optional[RegionData]:
        """获取指定区域的瞬态数据"""
        return self.transient_data.get(region_name)
        
    def get_flow_data_regions(self, data_source: str) -> Dict[str, RegionData]:
        """
        获取流体数据区域
        
        Args:
            data_source: "steady" 或 "transient"
            
        Returns:
            Dict[str, RegionData]: 区域数据字典
        """
        if data_source == "steady":
            return self.steady_data
        elif data_source == "transient":
            return self.transient_data
        else:
            return {}
            
    def has_mesh_data(self) -> bool:
        """检查是否有网格数据"""
        return len(self.mesh_data) > 0

    def has_any_mesh_data(self) -> bool:
        """检查是否有任何可用于网格渲染的数据（包括稳态和瞬态数据）"""
        return (len(self.mesh_data) > 0 or
                len(self.steady_data) > 0 or
                len(self.transient_data) > 0)
        
    def has_steady_data(self) -> bool:
        """检查是否有稳态数据"""
        return len(self.steady_data) > 0
        
    def has_transient_data(self) -> bool:
        """检查是否有瞬态数据"""
        return len(self.transient_data) > 0

    def _prepare_3d_streamline_data(self, data_source: str, progress_callback: Optional[ProgressCallback] = None):
        """预处理数据为3D流线追踪格式"""
        try:
            print(f"=== 预处理{data_source}数据为3D流线格式 ===")

            # 获取数据源
            if data_source == "steady":
                data_dict = self.steady_data
            elif data_source == "transient":
                data_dict = self.transient_data
            else:
                return

            if not data_dict:
                return

            # 存储预处理后的VTK网格
            if not hasattr(self, '_prepared_vtk_grids'):
                self._prepared_vtk_grids = {}

            self._prepared_vtk_grids[data_source] = {}

            total_regions = len(data_dict)
            processed = 0

            for region_name, region_data in data_dict.items():
                if progress_callback:
                    progress = 50 + int((processed / total_regions) * 40)
                    progress_callback.update(progress, f"预处理区域 {region_name}...")

                # 创建VTK网格并预处理为3D格式
                vtk_grid = self._create_and_prepare_vtk_grid(region_data, region_name)
                if vtk_grid:
                    self._prepared_vtk_grids[data_source][region_name] = vtk_grid
                    print(f"✓ 区域 {region_name}: 3D网格预处理完成")
                else:
                    print(f"⚠️ 区域 {region_name}: 3D网格预处理失败")

                processed += 1

            print(f"✓ {data_source}数据3D预处理完成: {len(self._prepared_vtk_grids[data_source])} 个区域")

        except Exception as e:
            print(f"3D数据预处理失败: {e}")

    def get_prepared_vtk_grids(self, data_source: str) -> dict:
        """获取预处理的VTK网格"""
        if hasattr(self, '_prepared_vtk_grids') and data_source in self._prepared_vtk_grids:
            return self._prepared_vtk_grids[data_source]
        return {}

    def _create_and_prepare_vtk_grid(self, region_data: RegionData, region_name: str):
        """创建并预处理VTK网格为3D流线格式"""
        try:
            import vtk

            # 创建基础VTK网格
            vtk_grid = self._create_basic_vtk_grid(region_data, region_name)
            if vtk_grid is None:
                return None

            # 预处理为3D流线格式
            processed_grid = self._prepare_grid_for_3d_streamlines(vtk_grid)

            return processed_grid

        except Exception as e:
            print(f"创建3D VTK网格失败 {region_name}: {e}")
            return None
        
    def has_flow_data(self, data_source: str) -> bool:
        """检查是否有流体数据"""
        if data_source == "steady":
            return self.has_steady_data()
        elif data_source == "transient":
            return self.has_transient_data()
        return False
        
    def get_combined_bounds(self, region_names: List[str], data_source: str = "mesh") -> Optional[Tuple[float, float, float, float, float, float]]:
        """
        获取多个区域的组合边界
        
        Args:
            region_names: 区域名称列表
            data_source: 数据源类型 ("mesh", "steady", "transient")
            
        Returns:
            Tuple: [xmin, xmax, ymin, ymax, zmin, zmax] 或 None
        """
        if data_source == "mesh":
            data_dict = self.mesh_data
        elif data_source == "steady":
            data_dict = self.steady_data
        elif data_source == "transient":
            data_dict = self.transient_data
        else:
            return None
            
        bounds = [float('inf'), float('-inf')] * 3  # [xmin, xmax, ymin, ymax, zmin, zmax]
        
        for region_name in region_names:
            if region_name in data_dict:
                region_bounds = data_dict[region_name].get_bounds()
                bounds[0] = min(bounds[0], region_bounds[0])
                bounds[1] = max(bounds[1], region_bounds[1])
                bounds[2] = min(bounds[2], region_bounds[2])
                bounds[3] = max(bounds[3], region_bounds[3])
                bounds[4] = min(bounds[4], region_bounds[4])
                bounds[5] = max(bounds[5], region_bounds[5])
                
        return tuple(bounds) if bounds[0] != float('inf') else None
        
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要信息"""
        summary = {
            'mesh_data': {
                'region_count': len(self.mesh_data),
                'regions': list(self.mesh_data.keys()),
                'total_points': sum(len(region) for region in self.mesh_data.values()),
                'has_faces': any(region.has_faces() for region in self.mesh_data.values())
            },
            'steady_data': {
                'region_count': len(self.steady_data),
                'regions': list(self.steady_data.keys()),
                'total_points': sum(len(region) for region in self.steady_data.values())
            },
            'transient_data': {
                'region_count': len(self.transient_data),
                'regions': list(self.transient_data.keys()),
                'total_points': sum(len(region) for region in self.transient_data.values())
            }
        }
        
        return summary
        
    def get_pressure_info(self, data_source: str) -> str:
        """获取压力数据信息"""
        data_dict = self.get_flow_data_regions(data_source)
        if not data_dict:
            return "没有数据"
            
        pressure_info = []
        
        for region_name, region_data in data_dict.items():
            info = region_data.get_info()
            if 'pressure' in info:
                pressure_info.append(
                    f"区域 {region_name}:\n"
                    f"  压力列: {info['pressure']['column']}\n"
                    f"  范围: {info['pressure']['min']:.2f} ~ {info['pressure']['max']:.2f} Pa\n"
                    f"  平均值: {info['pressure']['mean']:.2f} Pa"
                )
            else:
                pressure_info.append(
                    f"区域 {region_name}:\n"
                    f"  ❌ 未找到压力数据列"
                )
                
        return "\n\n".join(pressure_info) if pressure_info else "未找到任何压力数据"
        
    def clear_all_data(self):
        """清空所有数据"""
        self.mesh_data.clear()
        self.steady_data.clear()
        self.transient_data.clear()
        print("所有数据已清空")
        
    def clear_mesh_data(self):
        """清空网格数据"""
        self.mesh_data.clear()
        print("网格数据已清空")
        
    def clear_steady_data(self):
        """清空稳态数据"""
        self.steady_data.clear()
        print("稳态数据已清空")
        
    def clear_transient_data(self):
        """清空瞬态数据"""
        self.transient_data.clear()
        print("瞬态数据已清空")
